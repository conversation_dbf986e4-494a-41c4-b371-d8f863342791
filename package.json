{"name": "project-2025-nhb-gamification-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "19.1.1", "react-dom": "19.1.1", "next": "15.4.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.5", "@eslint/eslintrc": "^3"}}