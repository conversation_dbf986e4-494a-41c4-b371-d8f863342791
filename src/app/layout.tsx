import "./globals.css";

import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import Script from "next/script";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {/* ts-ignore - SGDS */}
        <sgds-masthead />
        {children}
        <Script
          src="https://cdn.jsdelivr.net/npm/@govtechsg/sgds-web-component@3.1.1/components/Masthead/index.umd.js"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
